using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SaveDataService;
using SaveDataService.Manage;

namespace ComfyuiGate
{
    /// <summary>
    /// AceStepAll - ComfyUI工作流调用类
    /// 基于文件: ace-step-all.json
    /// 自动生成时间: 2025-06-05 16:48:27
    /// </summary>
    public class AceStepAll
    {
        /// <summary>
        /// 工作流JSON定义
        /// </summary>
        private const string WORKFLOW_JSON = @"{""id"":""28f25528-b30c-4a0b-8573-f1c7b98cc10b"",""revision"":0,""last_node_id"":32,""last_link_id"":36,""nodes"":[{""id"":2,""type"":""MultiLinePromptACES"",""pos"":[1058.153076171875,120.83321380615234],""size"":[275.70098876953125,109.34579467773438],""flags"":{},""order"":0,""mode"":0,""inputs"":[{""localized_name"":""multi_line_prompt"",""name"":""multi_line_prompt"",""type"":""STRING"",""widget"":{""name"":""multi_line_prompt""},""link"":null}],""outputs"":[{""localized_name"":""prompt"",""name"":""prompt"",""type"":""STRING"",""links"":[10]}],""properties"":{""cnr_id"":""ace-step"",""ver"":""fa633f1fc8b83747bdf944f2bbcdb3ec46db16b1"",""Node name for S&R"":""MultiLinePromptACES"",""widget_ue_connectable"":{""multi_line_prompt"":true}},""widgets_values"":[""hip-house, funk"",[false,true]]},{""id"":8,""type"":""ACEStepGen"",""pos"":[1416.3770751953125,155.59963989257812],""size"":[264.85986328125,98],""flags"":{},""order"":5,""mode"":0,""inputs"":[{""localized_name"":""prompt"",""name"":""prompt"",""type"":""STRING"",""link"":10},{""localized_name"":""lyrics"",""name"":""lyrics"",""type"":""STRING"",""link"":11},{""localized_name"":""parameters"",""name"":""parameters"",""type"":""STRING"",""link"":12},{""localized_name"":""unload_model"",""name"":""unload_model"",""type"":""BOOLEAN"",""widget"":{""name"":""unload_model""},""link"":null}],""outputs"":[{""localized_name"":""music"",""name"":""music"",""type"":""AUDIO"",""links"":[13]}],""properties"":{""cnr_id"":""ace-step"",""ver"":""fa633f1fc8b83747bdf944f2bbcdb3ec46db16b1"",""Node name for S&R"":""ACEStepGen"",""widget_ue_connectable"":{""unload_model"":true}},""widgets_values"":[true]},{""id"":1,""type"":""MultiLineLyrics"",""pos"":[748.9937744140625,146.34722900390625],""size"":[279.4393005371094,492.5234375],""flags"":{},""order"":1,""mode"":0,""inputs"":[{""localized_name"":""multi_line_prompt"",""name"":""multi_line_prompt"",""type"":""STRING"",""widget"":{""name"":""multi_line_prompt""},""link"":null}],""outputs"":[{""localized_name"":""lyrics"",""name"":""lyrics"",""type"":""STRING"",""links"":[11]}],""title"":""input-musictxt-歌词"",""properties"":{""cnr_id"":""ace-step"",""ver"":""fa633f1fc8b83747bdf944f2bbcdb3ec46db16b1"",""Node name for S&R"":""MultiLineLyrics"",""widget_ue_connectable"":{""multi_line_prompt"":true}},""widgets_values"":[""[verse]\n哎呀跳起来，脚尖踩节拍 (oo-yeah!)\n灯光闪烁像星星盛开 (uh-huh!)\n人人都醒来，把烦恼踹开 (get it!)\n热血沸腾，汗水自己安排\n\n[chorus]\n嘿，你还等啥？快抓住节拍 (come on!)\n光芒指引，让心都不存在 (whoa!)\n点燃热火，我们一起飙high (let’s go!)\n跳入午夜的狂欢时代\n\n[bridge]\n咚咚鼓声啊，让你的灵魂起飞 (woo!)\n手心拍一拍，能量翻倍 (ah-hah!)\n键盘响起来，如宇宙的交汇 (oh yeah!)\n就是这感觉，兄弟姐妹都陶醉\n\n[verse]\n灵魂从不睡，只想继续燃烧 (woo!)\n节奏像热浪，席卷这街道 (ow!)\n大伙儿涌上楼台，满面微笑 (yeah!)\n这一刻属于我们，无可替代\n\n[chorus]\n嘿，你还等啥？快抓住节拍 (come on!)\n光芒指引，让心都不存在 (whoa!)\n点燃热火，我们一起飙high (let’s go!)\n跳入午夜的狂欢时代\n\n[verse]\n世界多精彩，握紧把它打开 (alright!)\n每一步都像星球在摇摆 (uh-huh!)\n无边无际的律动像大海 (oo-yeah!)\n跟着光芒之舞，一起澎湃"",[false,true]]},{""id"":31,""type"":""PrimitiveInt"",""pos"":[738.5838012695312,691.8623046875],""size"":[270,82],""flags"":{},""order"":2,""mode"":0,""inputs"":[{""localized_name"":""数值"",""name"":""value"",""type"":""INT"",""widget"":{""name"":""value""},""link"":null}],""outputs"":[{""localized_name"":""整数"",""name"":""INT"",""type"":""INT"",""links"":[35]}],""title"":""随机种子"",""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.39"",""Node name for S&R"":""PrimitiveInt"",""widget_ue_connectable"":{}},""widgets_values"":[0,""randomize""]},{""id"":5,""type"":""GenerationParameters"",""pos"":[1072.0526123046875,275.9087829589844],""size"":[296.76171875,442],""flags"":{},""order"":4,""mode"":0,""inputs"":[{""localized_name"":""audio_duration"",""name"":""audio_duration"",""type"":""FLOAT"",""widget"":{""name"":""audio_duration""},""link"":null},{""localized_name"":""infer_step"",""name"":""infer_step"",""type"":""INT"",""widget"":{""name"":""infer_step""},""link"":36},{""localized_name"":""guidance_scale"",""name"":""guidance_scale"",""type"":""FLOAT"",""widget"":{""name"":""guidance_scale""},""link"":null},{""localized_name"":""scheduler_type"",""name"":""scheduler_type"",""type"":""COMBO"",""widget"":{""name"":""scheduler_type""},""link"":null},{""localized_name"":""cfg_type"",""name"":""cfg_type"",""type"":""COMBO"",""widget"":{""name"":""cfg_type""},""link"":null},{""localized_name"":""omega_scale"",""name"":""omega_scale"",""type"":""FLOAT"",""widget"":{""name"":""omega_scale""},""link"":null},{""localized_name"":""seed"",""name"":""seed"",""type"":""INT"",""widget"":{""name"":""seed""},""link"":35},{""localized_name"":""guidance_interval"",""name"":""guidance_interval"",""type"":""FLOAT"",""widget"":{""name"":""guidance_interval""},""link"":null},{""localized_name"":""guidance_interval_decay"",""name"":""guidance_interval_decay"",""type"":""FLOAT"",""widget"":{""name"":""guidance_interval_decay""},""link"":null},{""localized_name"":""min_guidance_scale"",""name"":""min_guidance_scale"",""type"":""INT"",""widget"":{""name"":""min_guidance_scale""},""link"":null},{""localized_name"":""use_erg_tag"",""name"":""use_erg_tag"",""type"":""BOOLEAN"",""widget"":{""name"":""use_erg_tag""},""link"":null},{""localized_name"":""use_erg_lyric"",""name"":""use_erg_lyric"",""type"":""BOOLEAN"",""widget"":{""name"":""use_erg_lyric""},""link"":null},{""localized_name"":""use_erg_diffusion"",""name"":""use_erg_diffusion"",""type"":""BOOLEAN"",""widget"":{""name"":""use_erg_diffusion""},""link"":null},{""localized_name"":""oss_steps"",""name"":""oss_steps"",""type"":""STRING"",""widget"":{""name"":""oss_steps""},""link"":null},{""localized_name"":""guidance_scale_text"",""name"":""guidance_scale_text"",""type"":""FLOAT"",""widget"":{""name"":""guidance_scale_text""},""link"":null},{""localized_name"":""guidance_scale_lyric"",""name"":""guidance_scale_lyric"",""type"":""FLOAT"",""widget"":{""name"":""guidance_scale_lyric""},""link"":null}],""outputs"":[{""localized_name"":""parameters"",""name"":""parameters"",""type"":""STRING"",""links"":[12]}],""properties"":{""cnr_id"":""ace-step"",""ver"":""fa633f1fc8b83747bdf944f2bbcdb3ec46db16b1"",""Node name for S&R"":""GenerationParameters"",""widget_ue_connectable"":{""audio_duration"":true,""infer_step"":true,""guidance_scale"":true,""scheduler_type"":true,""cfg_type"":true,""omega_scale"":true,""seed"":true,""guidance_interval"":true,""guidance_interval_decay"":true,""min_guidance_scale"":true,""use_erg_tag"":true,""use_erg_lyric"":true,""use_erg_diffusion"":true,""oss_steps"":true,""guidance_scale_text"":true,""guidance_scale_lyric"":true}},""widgets_values"":[204.19997916666668,60,15,""euler"",""apg"",10,3058990169,""fixed"",0.5,0,3,true,true,true,"""",0,0]},{""id"":32,""type"":""easy int"",""pos"":[741.2386474609375,827.2355346679688],""size"":[270,58],""flags"":{},""order"":3,""mode"":0,""inputs"":[{""localized_name"":""值"",""name"":""value"",""type"":""INT"",""widget"":{""name"":""value""},""link"":null}],""outputs"":[{""localized_name"":""整数"",""name"":""int"",""type"":""INT"",""links"":[36]}],""title"":""input-step-步数"",""properties"":{""cnr_id"":""comfyui-easy-use"",""ver"":""1.3.0"",""Node name for S&R"":""easy int"",""widget_ue_connectable"":{}},""widgets_values"":[60]},{""id"":6,""type"":""SaveAudioMW"",""pos"":[1742.7762451171875,148.224609375],""size"":[270,82],""flags"":{},""order"":6,""mode"":0,""inputs"":[{""localized_name"":""audio"",""name"":""audio"",""type"":""AUDIO"",""link"":13},{""localized_name"":""format"",""name"":""format"",""type"":""COMBO"",""widget"":{""name"":""format""},""link"":null},{""localized_name"":""filename_prefix"",""name"":""filename_prefix"",""type"":""STRING"",""widget"":{""name"":""filename_prefix""},""link"":null}],""outputs"":[],""title"":""output-audio-ace"",""properties"":{""cnr_id"":""audiotools"",""ver"":""1.0.7"",""Node name for S&R"":""SaveAudioMW"",""widget_ue_connectable"":{""format"":true,""filename_prefix"":true}},""widgets_values"":[""WAV"",""1""]}],""links"":[[10,2,0,8,0,""STRING""],[11,1,0,8,1,""STRING""],[12,5,0,8,2,""STRING""],[13,8,0,6,0,""AUDIO""],[35,31,0,5,6,""INT""],[36,32,0,5,1,""INT""]],""groups"":[{""id"":1,""title"":""Group"",""bounding"":[738.9937744140625,47.23321533203125,957.476806640625,682.7029418945312],""color"":""#3f789e"",""font_size"":24,""flags"":{}}],""config"":{},""extra"":{""frontendVersion"":""1.19.1"",""VHS_latentpreview"":false,""VHS_latentpreviewrate"":0,""VHS_MetadataImage"":true,""VHS_KeepIntermediate"":true,""ue_links"":[],""ds"":{""scale"":0.9433219261238046,""offset"":[-125.30324866890248,175.4157425844822]}},""version"":0.4}";

        /// <summary>
        /// 运行工作流
        /// </summary>
        /// <param name="musictxt_multi_line_prompt">input-musictxt-歌词 - multi_line_prompt</param>
        /// <param name="step_value">input-step-步数 - value</param>
        /// <returns>任务ID</returns>
        public static string runWorkflow(string musictxt_multi_line_prompt = @"[verse]
哎呀跳起来，脚尖踩节拍 (oo-yeah!)
灯光闪烁像星星盛开 (uh-huh!)
人人都醒来，把烦恼踹开 (get it!)
热血沸腾，汗水自己安排

[chorus]
嘿，你还等啥？快抓住节拍 (come on!)
光芒指引，让心都不存在 (whoa!)
点燃热火，我们一起飙high (let’s go!)
跳入午夜的狂欢时代

[bridge]
咚咚鼓声啊，让你的灵魂起飞 (woo!)
手心拍一拍，能量翻倍 (ah-hah!)
键盘响起来，如宇宙的交汇 (oh yeah!)
就是这感觉，兄弟姐妹都陶醉

[verse]
灵魂从不睡，只想继续燃烧 (woo!)
节奏像热浪，席卷这街道 (ow!)
大伙儿涌上楼台，满面微笑 (yeah!)
这一刻属于我们，无可替代

[chorus]
嘿，你还等啥？快抓住节拍 (come on!)
光芒指引，让心都不存在 (whoa!)
点燃热火，我们一起飙high (let’s go!)
跳入午夜的狂欢时代

[verse]
世界多精彩，握紧把它打开 (alright!)
每一步都像星球在摇摆 (uh-huh!)
无边无际的律动像大海 (oo-yeah!)
跟着光芒之舞，一起澎湃", int step_value = 60)
        {
            try
            {
                // 解析工作流JSON
                var workflow = JsonConvert.DeserializeObject<JObject>(WORKFLOW_JSON);
                if (workflow == null)
                {
                    throw new Exception("无法解析工作流JSON");
                }

                // 更新输入参数
                // 检查JSON格式并相应更新参数
                if (workflow["nodes"] != null)
                {
                    // 新格式：包含nodes数组
                    var nodesArray = workflow["nodes"] as JArray;
                    if (nodesArray != null)
                    {
                        // 更新节点 1 的 multi_line_prompt 参数
                        var node1 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "1") as JObject;
                        if (node1 != null)
                        {
                            var inputs = node1["inputs"] as JArray;
                            if (inputs != null)
                            {
                                var inputIndex = inputs.ToList().FindIndex(i => i["name"]?.ToString() == "multi_line_prompt");
                                if (inputIndex >= 0)
                                {
                                    var widgetValues = node1["widgets_values"] as JArray;
                                    if (widgetValues != null && inputIndex < widgetValues.Count)
                                    {
                                        widgetValues[inputIndex] = JToken.FromObject(musictxt_multi_line_prompt);
                                    }
                                }
                            }
                        }

                        // 更新节点 32 的 value 参数
                        var node32 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "32") as JObject;
                        if (node32 != null)
                        {
                            var inputs = node32["inputs"] as JArray;
                            if (inputs != null)
                            {
                                var inputIndex = inputs.ToList().FindIndex(i => i["name"]?.ToString() == "value");
                                if (inputIndex >= 0)
                                {
                                    var widgetValues = node32["widgets_values"] as JArray;
                                    if (widgetValues != null && inputIndex < widgetValues.Count)
                                    {
                                        widgetValues[inputIndex] = JToken.FromObject(step_value);
                                    }
                                }
                            }
                        }

                    }
                }
                else
                {
                    // 旧格式：直接以节点ID为键
                    // 更新节点 1 的 multi_line_prompt 参数
                    if (workflow["1"]?["inputs"]?["multi_line_prompt"] != null)
                    {
                        workflow["1"]["inputs"]["multi_line_prompt"] = JToken.FromObject(musictxt_multi_line_prompt);
                    }

                    // 更新节点 32 的 value 参数
                    if (workflow["32"]?["inputs"]?["value"] != null)
                    {
                        workflow["32"]["inputs"]["value"] = JToken.FromObject(step_value);
                    }

                }
                // 提交工作流到ComfyUI
                string updatedJson = JsonConvert.SerializeObject(workflow);
                var comfyUIManage = ComfyUIManage.Instance;

                // 首先将工作流保存到数据库（如果不存在）
                string workflowId = EnsureWorkflowExists(updatedJson, "AceStepAll");
                if (string.IsNullOrEmpty(workflowId))
                {
                    Console.WriteLine("无法保存工作流到数据库");
                    return "";
                }

                // 创建任务
                string taskId = comfyUIManage.CreateTask(workflowId, "AceStepAll_Task", "system");
                if (string.IsNullOrEmpty(taskId))
                {
                    Console.WriteLine("无法创建任务");
                    return "";
                }

                Console.WriteLine($"工作流已提交，任务ID: {taskId}");
                return taskId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"运行工作流失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务状态信息</returns>
        public static string GetTaskStatus(string taskId)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                var task = comfyUIManage.GetTaskById(taskId);
                if (task != null)
                {
                    return JsonConvert.SerializeObject(task, Formatting.Indented);
                }
                return "任务不存在";
            }
            catch (Exception ex)
            {
                return $"获取任务状态失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 确保工作流存在于数据库中
        /// </summary>
        /// <param name="workflowJson">工作流JSON</param>
        /// <param name="workflowName">工作流名称</param>
        /// <returns>工作流ID</returns>
        private static string EnsureWorkflowExists(string workflowJson, string workflowName)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;

                // 计算工作流的哈希值作为唯一标识
                string workflowHash = System.Security.Cryptography.SHA1.Create()
                    .ComputeHash(System.Text.Encoding.UTF8.GetBytes(workflowJson))
                    .Aggregate("", (s, b) => s + b.ToString("x2"));

                // 检查是否已存在
                var existingWorkflow = comfyUIManage.GetWorkflowById(workflowHash);
                if (existingWorkflow != null)
                {
                    Console.WriteLine($"工作流已存在: {workflowHash}");
                    return workflowHash;
                }

                // 创建新的工作流
                string workflowId = comfyUIManage.AddWorkflow(
                    workflowName,
                    workflowJson,
                    "generated",
                    $"自动生成的工作流: {workflowName}",
                    "system"
                );

                Console.WriteLine($"创建新工作流: {workflowId}");
                return workflowId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"确保工作流存在失败: {ex.Message}");
                return "";
            }
        }

    }
}
