using System;
using System.Threading.Tasks;
using SaveDataService.Test;
using SaveDataService.Manage;

namespace SaveDataService
{
    /// <summary>
    /// 测试 AddWorkflows 方法的主程序
    /// </summary>
    public class TestAddWorkflows
    {
        public static async Task RunTest()
        {
            Console.WriteLine("🚀 ComfyUI AddWorkflows 功能测试程序");
            Console.WriteLine();

            try
            {
                // 运行完整测试
                await ComfyUIAddWorkflowsTest.RunAddWorkflowsTest();

                Console.WriteLine();
                Console.WriteLine("测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"程序执行错误: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
            }
        }

        public static void RunQuickTest()
        {
            Console.WriteLine("⚡ ComfyUI AddWorkflows 快速测试");
            Console.WriteLine();

            try
            {
                var comfyUIManage = ComfyUIManage.Instance;

                Console.WriteLine("执行 AddWorkflows...");
                var result = comfyUIManage.AddWorkflows();
                Console.WriteLine(result);

                var workflows = comfyUIManage.GetAllWorkflows();
                Console.WriteLine($"\n导入完成，共 {workflows.Count} 个工作流");

                foreach (var workflow in workflows)
                {
                    Console.WriteLine($"- {workflow.workflowName} ({workflow.workflowType})");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
            }
        }
    }
}
