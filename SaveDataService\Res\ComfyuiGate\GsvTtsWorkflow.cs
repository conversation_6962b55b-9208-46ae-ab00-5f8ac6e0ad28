﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SaveDataService;
using SaveDataService.Manage;

namespace ComfyuiGate
{
    /// <summary>
    /// GsvTtsWorkflow - ComfyUI工作流调用类
    /// 基于文件: gsv_tts_workflow.json
    /// 自动生成时间: 2025-06-05 16:48:27
    /// </summary>
    public class GsvTtsWorkflow
    {
        /// <summary>
        /// 工作流JSON定义
        /// </summary>
        private const string WORKFLOW_JSON = @"{""id"":""c88e3638-0284-45ba-9b3d-afe401fd8209"",""revision"":0,""last_node_id"":10,""last_link_id"":13,""nodes"":[{""id"":6,""type"":""ExperienceNode"",""pos"":[172.40733337402344,555.0353393554688],""size"":[315,178],""flags"":{},""order"":0,""mode"":0,""inputs"":[{""localized_name"":""exp_name"",""name"":""exp_name"",""type"":""STRING"",""widget"":{""name"":""exp_name""},""link"":null},{""localized_name"":""version"",""name"":""version"",""type"":""COMBO"",""widget"":{""name"":""version""},""link"":null},{""localized_name"":""is_half"",""name"":""is_half"",""type"":""BOOLEAN"",""widget"":{""name"":""is_half""},""link"":null},{""localized_name"":""if_redataset"",""name"":""if_redataset"",""type"":""BOOLEAN"",""widget"":{""name"":""if_redataset""},""link"":null},{""localized_name"":""if_ft_sovits"",""name"":""if_ft_sovits"",""type"":""BOOLEAN"",""widget"":{""name"":""if_ft_sovits""},""link"":null},{""localized_name"":""if_ft_gpt"",""name"":""if_ft_gpt"",""type"":""BOOLEAN"",""widget"":{""name"":""if_ft_gpt""},""link"":null}],""outputs"":[{""localized_name"":""CONFIG"",""name"":""CONFIG"",""type"":""CONFIG"",""links"":[11]}],""properties"":{""cnr_id"":""GSTTS-ComfyUI"",""ver"":""da760a2b3ebd20120e3644c46a8d686ffd4ec9ba"",""Node name for S&R"":""ExperienceNode"",""widget_ue_connectable"":{}},""widgets_values"":[""aifsh"",""v2"",true,true,true,true]},{""id"":4,""type"":""LoadAudio"",""pos"":[153.5635986328125,-118.30266571044922],""size"":[315,136],""flags"":{},""order"":1,""mode"":0,""inputs"":[{""localized_name"":""音频"",""name"":""audio"",""type"":""COMBO"",""widget"":{""name"":""audio""},""link"":null},{""localized_name"":""音频UI"",""name"":""audioUI"",""type"":""AUDIO_UI"",""widget"":{""name"":""audioUI""},""link"":null},{""localized_name"":""选择文件上传"",""name"":""upload"",""type"":""AUDIOUPLOAD"",""widget"":{""name"":""upload""},""link"":null}],""outputs"":[{""localized_name"":""音频"",""name"":""AUDIO"",""type"":""AUDIO"",""links"":[10]}],""title"":""input-audio-参考人声音频"",""properties"":{""cnr_id"":""comfy-core"",""ver"":""0.3.39"",""Node name for S&R"":""LoadAudio"",""widget_ue_connectable"":{}},""widgets_values"":[""委屈-要不是溜出来的时候身上没带够钱，本小姐也不至于…….wav"",null,null]},{""id"":2,""type"":""TextDictNode"",""pos"":[120.57051849365234,64.7601089477539],""size"":[400,200],""flags"":{},""order"":2,""mode"":0,""inputs"":[{""localized_name"":""text"",""name"":""text"",""type"":""STRING"",""widget"":{""name"":""text""},""link"":null},{""localized_name"":""language"",""name"":""language"",""type"":""COMBO"",""widget"":{""name"":""language""},""link"":null}],""outputs"":[{""localized_name"":""TEXTDICT"",""name"":""TEXTDICT"",""type"":""TEXTDICT"",""links"":[8]}],""title"":""input-text-需要的台词"",""properties"":{""cnr_id"":""GSTTS-ComfyUI"",""ver"":""da760a2b3ebd20120e3644c46a8d686ffd4ec9ba"",""Node name for S&R"":""TextDictNode"",""widget_ue_connectable"":{}},""widgets_values"":[""我是杨思纯，是兄弟就来砍我"",""中文"",[false,true]]},{""id"":3,""type"":""TextDictNode"",""pos"":[113.46512603759766,308.2427062988281],""size"":[400,200],""flags"":{},""order"":3,""mode"":0,""inputs"":[{""localized_name"":""text"",""name"":""text"",""type"":""STRING"",""widget"":{""name"":""text""},""link"":null},{""localized_name"":""language"",""name"":""language"",""type"":""COMBO"",""widget"":{""name"":""language""},""link"":null}],""outputs"":[{""localized_name"":""TEXTDICT"",""name"":""TEXTDICT"",""type"":""TEXTDICT"",""links"":[9]}],""title"":""input-text-还没测试的输入暂时不输入"",""properties"":{""cnr_id"":""GSTTS-ComfyUI"",""ver"":""da760a2b3ebd20120e3644c46a8d686ffd4ec9ba"",""Node name for S&R"":""TextDictNode"",""widget_ue_connectable"":{}},""widgets_values"":["""",""中文"",[false,true]]},{""id"":8,""type"":""GSVTTSNode"",""pos"":[572.17529296875,-23.182174682617188],""size"":[370.9635925292969,546.4337158203125],""flags"":{},""order"":5,""mode"":0,""inputs"":[{""localized_name"":""text_dict"",""name"":""text_dict"",""type"":""TEXTDICT"",""link"":8},{""localized_name"":""prompt_text_dict"",""name"":""prompt_text_dict"",""type"":""TEXTDICT"",""link"":9},{""localized_name"":""prompt_audio"",""name"":""prompt_audio"",""type"":""AUDIO"",""link"":10},{""localized_name"":""config"",""name"":""config"",""type"":""CONFIG"",""link"":11},{""localized_name"":""GPT_weight"",""name"":""GPT_weight"",""type"":""COMBO"",""widget"":{""name"":""GPT_weight""},""link"":null},{""localized_name"":""SoVITS_weight"",""name"":""SoVITS_weight"",""type"":""COMBO"",""widget"":{""name"":""SoVITS_weight""},""link"":null},{""localized_name"":""how_to_cut"",""name"":""how_to_cut"",""type"":""COMBO"",""widget"":{""name"":""how_to_cut""},""link"":null},{""localized_name"":""speed"",""name"":""speed"",""type"":""FLOAT"",""widget"":{""name"":""speed""},""link"":null},{""localized_name"":""top_k"",""name"":""top_k"",""type"":""INT"",""widget"":{""name"":""top_k""},""link"":null},{""localized_name"":""top_p"",""name"":""top_p"",""type"":""FLOAT"",""widget"":{""name"":""top_p""},""link"":null},{""localized_name"":""temperature"",""name"":""temperature"",""type"":""FLOAT"",""widget"":{""name"":""temperature""},""link"":null}],""outputs"":[{""localized_name"":""音频"",""name"":""AUDIO"",""type"":""AUDIO"",""slot_index"":0,""links"":[12]}],""properties"":{""cnr_id"":""GSTTS-ComfyUI"",""ver"":""da760a2b3ebd20120e3644c46a8d686ffd4ec9ba"",""Node name for S&R"":""GSVTTSNode"",""widget_ue_connectable"":{}},""widgets_values"":[""gsv-v2final-pretrained/s1bert25hz-5kh-longer-epoch=12-step=369668.ckpt"",""gsv-v2final-pretrained/s2G2333k.pth"",""凑四句一切"",0.9800000000000002,15,1,1]},{""id"":9,""type"":""SaveAudio"",""pos"":[1038.8682861328125,-15.351277351379395],""size"":[270,112],""flags"":{},""order"":6,""mode"":0,""inputs"":[{""localized_name"":""音频"",""name"":""audio"",""type"":""AUDIO"",""link"":12},{""localized_name"":""文件名前缀"",""name"":""filename_prefix"",""type"":""STRING"",""widget"":{""name"":""filename_prefix""},""link"":13},{""localized_name"":""音频UI"",""name"":""audioUI"",""type"":""AUDIO_UI"",""widget"":{""name"":""audioUI""},""link"":null}],""outputs"":[],""title"":""output-audio-生成的音频"",""properties"":{""widget_ue_connectable"":{},""cnr_id"":""comfy-core"",""ver"":""0.3.39"",""Node name for S&R"":""SaveAudio""},""widgets_values"":[""audio/ComfyUI""]},{""id"":10,""type"":""CR Text"",""pos"":[554.4547729492188,-209.49371337890625],""size"":[396.2302551269531,113],""flags"":{},""order"":4,""mode"":0,""inputs"":[{""localized_name"":""text"",""name"":""text"",""type"":""STRING"",""widget"":{""name"":""text""},""link"":null}],""outputs"":[{""localized_name"":""text"",""name"":""text"",""type"":""*"",""links"":[13]},{""localized_name"":""show_help"",""name"":""show_help"",""type"":""STRING"",""links"":null}],""title"":""input-text-文件前缀"",""properties"":{""widget_ue_connectable"":{},""cnr_id"":""ComfyUI_Comfyroll_CustomNodes"",""ver"":""d78b780ae43fcf8c6b7c6505e6ffb4584281ceca"",""Node name for S&R"":""CR Text""},""widgets_values"":[""audio/comfy"",[false,true]]}],""links"":[[8,2,0,8,0,""TEXTDICT""],[9,3,0,8,1,""TEXTDICT""],[10,4,0,8,2,""AUDIO""],[11,6,0,8,3,""CONFIG""],[12,8,0,9,0,""AUDIO""],[13,10,0,9,1,""STRING""]],""groups"":[],""config"":{},""extra"":{""ds"":{""scale"":1.0610764609500007,""offset"":[657.5220135836112,572.3328144283527]},""ue_links"":[],""links_added_by_ue"":[]},""version"":0.4}";

        /// <summary>
        /// 运行工作流
        /// </summary>
        /// <param name="audio_audio">input-audio-参考人声音频 - audio</param>
        /// <param name="audio_audioUI">input-audio-参考人声音频 - audioUI</param>
        /// <param name="audio_upload">input-audio-参考人声音频 - upload</param>
        /// <param name="text_text">input-text-需要的台词 - text</param>
        /// <param name="text_language">input-text-需要的台词 - language</param>
        /// <param name="text_text1">input-text-还没测试的输入暂时不输入 - text</param>
        /// <param name="text_language1">input-text-还没测试的输入暂时不输入 - language</param>
        /// <param name="text_text2">input-text-文件前缀 - text</param>
        /// <returns>任务ID</returns>
        public static string runWorkflow(string audio_audio = "委屈-要不是溜出来的时候身上没带够钱，本小姐也不至于…….wav", string audio_audioUI = "", string audio_upload = "", string text_text = "我是杨思纯，是兄弟就来砍我", string text_language = "中文", string text_text1 = "", string text_language1 = "中文", string text_text2 = "audio/comfy")
        {
            try
            {
                // 解析工作流JSON
                var workflow = JsonConvert.DeserializeObject<JObject>(WORKFLOW_JSON);
                if (workflow == null)
                {
                    throw new Exception("无法解析工作流JSON");
                }

                // 更新输入参数
                // 检查JSON格式并相应更新参数
                if (workflow["nodes"] != null)
                {
                    // 新格式：包含nodes数组
                    var nodesArray = workflow["nodes"] as JArray;
                    if (nodesArray != null)
                    {
                        // 更新节点 4 的 audio 参数
                        var node4 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "4") as JObject;
                        if (node4 != null)
                        {
                            var inputs = node4["inputs"] as JArray;
                            if (inputs != null)
                            {
                                var inputIndex = inputs.ToList().FindIndex(i => i["name"]?.ToString() == "audio");
                                if (inputIndex >= 0)
                                {
                                    var widgetValues = node4["widgets_values"] as JArray;
                                    if (widgetValues != null && inputIndex < widgetValues.Count)
                                    {
                                        widgetValues[inputIndex] = JToken.FromObject(audio_audio);
                                    }
                                }
                            }
                        }

                        // 更新节点 4 的 audioUI 参数
                        var node4 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "4") as JObject;
                        if (node4 != null)
                        {
                            var inputs = node4["inputs"] as JArray;
                            if (inputs != null)
                            {
                                var inputIndex = inputs.ToList().FindIndex(i => i["name"]?.ToString() == "audioUI");
                                if (inputIndex >= 0)
                                {
                                    var widgetValues = node4["widgets_values"] as JArray;
                                    if (widgetValues != null && inputIndex < widgetValues.Count)
                                    {
                                        widgetValues[inputIndex] = JToken.FromObject(audio_audioUI);
                                    }
                                }
                            }
                        }

                        // 更新节点 4 的 upload 参数
                        var node4 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "4") as JObject;
                        if (node4 != null)
                        {
                            var inputs = node4["inputs"] as JArray;
                            if (inputs != null)
                            {
                                var inputIndex = inputs.ToList().FindIndex(i => i["name"]?.ToString() == "upload");
                                if (inputIndex >= 0)
                                {
                                    var widgetValues = node4["widgets_values"] as JArray;
                                    if (widgetValues != null && inputIndex < widgetValues.Count)
                                    {
                                        widgetValues[inputIndex] = JToken.FromObject(audio_upload);
                                    }
                                }
                            }
                        }

                        // 更新节点 2 的 text 参数
                        var node2 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "2") as JObject;
                        if (node2 != null)
                        {
                            var inputs = node2["inputs"] as JArray;
                            if (inputs != null)
                            {
                                var inputIndex = inputs.ToList().FindIndex(i => i["name"]?.ToString() == "text");
                                if (inputIndex >= 0)
                                {
                                    var widgetValues = node2["widgets_values"] as JArray;
                                    if (widgetValues != null && inputIndex < widgetValues.Count)
                                    {
                                        widgetValues[inputIndex] = JToken.FromObject(text_text);
                                    }
                                }
                            }
                        }

                        // 更新节点 2 的 language 参数
                        var node2 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "2") as JObject;
                        if (node2 != null)
                        {
                            var inputs = node2["inputs"] as JArray;
                            if (inputs != null)
                            {
                                var inputIndex = inputs.ToList().FindIndex(i => i["name"]?.ToString() == "language");
                                if (inputIndex >= 0)
                                {
                                    var widgetValues = node2["widgets_values"] as JArray;
                                    if (widgetValues != null && inputIndex < widgetValues.Count)
                                    {
                                        widgetValues[inputIndex] = JToken.FromObject(text_language);
                                    }
                                }
                            }
                        }

                        // 更新节点 3 的 text 参数
                        var node3 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "3") as JObject;
                        if (node3 != null)
                        {
                            var inputs = node3["inputs"] as JArray;
                            if (inputs != null)
                            {
                                var inputIndex = inputs.ToList().FindIndex(i => i["name"]?.ToString() == "text");
                                if (inputIndex >= 0)
                                {
                                    var widgetValues = node3["widgets_values"] as JArray;
                                    if (widgetValues != null && inputIndex < widgetValues.Count)
                                    {
                                        widgetValues[inputIndex] = JToken.FromObject(text_text1);
                                    }
                                }
                            }
                        }

                        // 更新节点 3 的 language 参数
                        var node3 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "3") as JObject;
                        if (node3 != null)
                        {
                            var inputs = node3["inputs"] as JArray;
                            if (inputs != null)
                            {
                                var inputIndex = inputs.ToList().FindIndex(i => i["name"]?.ToString() == "language");
                                if (inputIndex >= 0)
                                {
                                    var widgetValues = node3["widgets_values"] as JArray;
                                    if (widgetValues != null && inputIndex < widgetValues.Count)
                                    {
                                        widgetValues[inputIndex] = JToken.FromObject(text_language1);
                                    }
                                }
                            }
                        }

                        // 更新节点 10 的 text 参数
                        var node10 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "10") as JObject;
                        if (node10 != null)
                        {
                            var inputs = node10["inputs"] as JArray;
                            if (inputs != null)
                            {
                                var inputIndex = inputs.ToList().FindIndex(i => i["name"]?.ToString() == "text");
                                if (inputIndex >= 0)
                                {
                                    var widgetValues = node10["widgets_values"] as JArray;
                                    if (widgetValues != null && inputIndex < widgetValues.Count)
                                    {
                                        widgetValues[inputIndex] = JToken.FromObject(text_text2);
                                    }
                                }
                            }
                        }

                    }
                }
                else
                {
                    // 旧格式：直接以节点ID为键
                    // 更新节点 4 的 audio 参数
                    if (workflow["4"]?["inputs"]?["audio"] != null)
                    {
                        workflow["4"]["inputs"]["audio"] = JToken.FromObject(audio_audio);
                    }

                    // 更新节点 4 的 audioUI 参数
                    if (workflow["4"]?["inputs"]?["audioUI"] != null)
                    {
                        workflow["4"]["inputs"]["audioUI"] = JToken.FromObject(audio_audioUI);
                    }

                    // 更新节点 4 的 upload 参数
                    if (workflow["4"]?["inputs"]?["upload"] != null)
                    {
                        workflow["4"]["inputs"]["upload"] = JToken.FromObject(audio_upload);
                    }

                    // 更新节点 2 的 text 参数
                    if (workflow["2"]?["inputs"]?["text"] != null)
                    {
                        workflow["2"]["inputs"]["text"] = JToken.FromObject(text_text);
                    }

                    // 更新节点 2 的 language 参数
                    if (workflow["2"]?["inputs"]?["language"] != null)
                    {
                        workflow["2"]["inputs"]["language"] = JToken.FromObject(text_language);
                    }

                    // 更新节点 3 的 text 参数
                    if (workflow["3"]?["inputs"]?["text"] != null)
                    {
                        workflow["3"]["inputs"]["text"] = JToken.FromObject(text_text1);
                    }

                    // 更新节点 3 的 language 参数
                    if (workflow["3"]?["inputs"]?["language"] != null)
                    {
                        workflow["3"]["inputs"]["language"] = JToken.FromObject(text_language1);
                    }

                    // 更新节点 10 的 text 参数
                    if (workflow["10"]?["inputs"]?["text"] != null)
                    {
                        workflow["10"]["inputs"]["text"] = JToken.FromObject(text_text2);
                    }

                }
                // 提交工作流到ComfyUI
                string updatedJson = JsonConvert.SerializeObject(workflow);
                var comfyUIManage = ComfyUIManage.Instance;

                // 首先将工作流保存到数据库（如果不存在）
                string workflowId = EnsureWorkflowExists(updatedJson, "GsvTtsWorkflow");
                if (string.IsNullOrEmpty(workflowId))
                {
                    Console.WriteLine("无法保存工作流到数据库");
                    return "";
                }

                // 创建任务
                string taskId = comfyUIManage.CreateTask(workflowId, "GsvTtsWorkflow_Task", "system");
                if (string.IsNullOrEmpty(taskId))
                {
                    Console.WriteLine("无法创建任务");
                    return "";
                }

                Console.WriteLine($"工作流已提交，任务ID: {taskId}");
                return taskId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"运行工作流失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务状态信息</returns>
        public static string GetTaskStatus(string taskId)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                var task = comfyUIManage.GetTaskById(taskId);
                if (task != null)
                {
                    return JsonConvert.SerializeObject(task, Formatting.Indented);
                }
                return "任务不存在";
            }
            catch (Exception ex)
            {
                return $"获取任务状态失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 确保工作流存在于数据库中
        /// </summary>
        /// <param name="workflowJson">工作流JSON</param>
        /// <param name="workflowName">工作流名称</param>
        /// <returns>工作流ID</returns>
        private static string EnsureWorkflowExists(string workflowJson, string workflowName)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;

                // 计算工作流的哈希值作为唯一标识
                string workflowHash = System.Security.Cryptography.SHA1.Create()
                    .ComputeHash(System.Text.Encoding.UTF8.GetBytes(workflowJson))
                    .Aggregate("", (s, b) => s + b.ToString("x2"));

                // 检查是否已存在
                var existingWorkflow = comfyUIManage.GetWorkflowById(workflowHash);
                if (existingWorkflow != null)
                {
                    Console.WriteLine($"工作流已存在: {workflowHash}");
                    return workflowHash;
                }

                // 创建新的工作流
                string workflowId = comfyUIManage.AddWorkflow(
                    workflowName,
                    workflowJson,
                    "generated",
                    $"自动生成的工作流: {workflowName}",
                    "system"
                );

                Console.WriteLine($"创建新工作流: {workflowId}");
                return workflowId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"确保工作流存在失败: {ex.Message}");
                return "";
            }
        }

    }
}
