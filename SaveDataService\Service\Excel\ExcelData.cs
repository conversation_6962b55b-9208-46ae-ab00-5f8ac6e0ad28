using GameServer;
using GameServer.Util;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;

namespace ExcelToData
{
    public class ExcelData
    {
        public static void ReadJSToText(string patch, bool needOutPut)
        {
            Console.WriteLine("开始读取的表格是:" + patch);

            //var fileAddress = System.IO.Path.Combine(Application.streamingAssetsPath, "MyTxt.txt");
            FileInfo fInfo0 = new FileInfo(patch);
            string text = "";
            if (fInfo0.Exists)
            {
                StreamReader r = new StreamReader(patch);
                //StreamReader默认的是UTF8的不需要转格式了，因为有些中文字符的需要有些是要转的，下面是转成String代码
                //byte[] data = new byte[1024];
                // data = Encoding.UTF8.GetBytes(r.ReadToEnd());
                // s = Encoding.UTF8.GetString(data, 0, data.Length);
                text = r.ReadToEnd();

                Regex reg = new Regex("\"[^\"]*\"");
                Regex reg2 = new Regex("[\u4e00-\u9fa5]");
                //用正则表达式把代码里面两种字符串中间的字符串提取出来。
                MatchCollection mc = reg.Matches(text);
                foreach (Match m in mc)
                {
                    string format = m.Value;
                    if (reg2.IsMatch(format))
                    {
                        string str = format.Replace("\"", "");
                        string strStr = str.Replace("\"", "");
                        Console.WriteLine(strStr);

                        if (cnDic.IndexOf(strStr) == -1)
                        {
                            cnDic.Add(strStr);
                            //Console.WriteLine(format);
                        }
                    }
                }
                //Console.WriteLine(text);
            }

            if (needOutPut)
            {
                string strstr = "";
                for (int k = 0; k < cnDic.Count; k++)
                {
                    strstr += cnDic[k] + "\n";
                }
                string outputUrl = AppConfig.Instance.excelResPath + "Assets/JS/CNCode.txt";
                using (FileStream fsWrite = new FileStream(outputUrl, FileMode.Create, FileAccess.Write))
                {
                    byte[] buffer = Encoding.Default.GetBytes(strstr);

                    fsWrite.Write(buffer, 0, buffer.Length);//无返回值，以字节数组的形式写入数据
                }
                Console.WriteLine("中文导出！   写入ok  导出路径为   " + outputUrl);
                //Console.ReadKey();
            }
        }
        static List<string> cnDic = new List<string>();
        /// <summary>
        /// 读取 excel 中的中文  生成文本
        /// </summary>
        /// <param name="patch"></param>
        public static void ReadExcelCNToText(string patch, bool needOutPut)
        {
            Console.WriteLine("开始读取的表格是:" + patch);
            //用来存储表头的配置信息
            Dictionary<string, string> excelInfo = new Dictionary<string, string>();
            //用来存储字段的存储的数据类型
            Dictionary<string, string> excelType = new Dictionary<string, string>();

            ///开始读取表格
            DataRowCollection collect = ExcelAccess.ReadExcel(patch, "config");
            //将excel表格中的配置信息放入到字典当中
            for (int i = 0; i < collect.Count; i++)
            {
                //Console.WriteLine("配置：" + collect[i][0] + "为：" + collect[i][1]);
                excelInfo[collect[i][0].ToString()] = collect[i][1].ToString();
            }

            Regex reg2 = new Regex("[\u4e00-\u9fa5]");
            ///开始读取表格
            DataRowCollection collect1 = ExcelAccess.ReadExcel(patch, excelInfo["dataTable"]);
            for (int t = 0; t < collect1[0].ItemArray.Length; t++)
            {
                for (int k = 0; k < collect1.Count; k++)
                {
                    string text = collect1[k][t].ToString();
                    //用正则表达式把代码里面两种字符串中间的字符串提取出来。
                    if (reg2.IsMatch(text))
                    {
                        MatchCollection mc = reg2.Matches(text);
                        foreach (Match m in mc)
                        {
                            string format = m.Value;
                            if (cnDic.IndexOf(format) == -1)
                            {
                                cnDic.Add(format);
                                //Console.WriteLine(format);
                            }
                        }
                    }
                }
            }
            if (needOutPut)
            {
                string strstr = "";
                for (int k = 0; k < cnDic.Count; k++)
                {
                    strstr += cnDic[k];
                }
                string outputUrl = AppConfig.Instance.excelResPath + "Assets/Data/CNCode.txt";
                using (FileStream fsWrite = new FileStream(outputUrl, FileMode.Create, FileAccess.Write))
                {
                    byte[] buffer = Encoding.Default.GetBytes(strstr);

                    fsWrite.Write(buffer, 0, buffer.Length);//无返回值，以字节数组的形式写入数据
                }
                Console.WriteLine("中文导出！   写入ok  导出路径为   " + outputUrl);
                //Console.ReadKey();
            }
        }

        public static void ExcuteBuild()
        {
            //Console.WriteLine(File.Exists(AppConfig.Instance.excelResPath + "Data/").ToString());
            //if (!File.Exists(Environment.CurrentDirectory.Replace("\\", "/") + "/Assets/Data/"))
            //{
            //    Console.WriteLine("文件路径：" + AppConfig.Instance.excelResPath + "Data/        不存在无法启动转换成程序");
            //    return;
            //}

            ////取出指定路径底下的所有excel文件
            //string[] filesPatch = Directory.GetFiles(AppConfig.Instance.excelResPath + "Data/", "*.xlsx", SearchOption.AllDirectories);
            //Console.WriteLine("路径：" + AppConfig.Instance.excelResPath + "Data/" + "中找到" + filesPatch.Length + "个文件");
            //ExportClass.Start();
            ////开始循环读excel的表头信息
            //for (int i = 0; i < filesPatch.Length; i++)
            //{
            //    ReadExcelHead(filesPatch[i]);
            //}
            //ExportClass.End();

            string baseDirectory = AppContext.BaseDirectory;
            Console.WriteLine($"基目录：{baseDirectory}");
            string modelFilePath = Directory.GetParent(baseDirectory).Parent.Parent.Parent.FullName;
            //Console.WriteLine(Directory.GetParent(baseDirectory));
            modelFilePath = modelFilePath.Replace("//", "/") + "/ORMModel.cs";
            //if (!File.Exists(modelFilePath))
            //{
                System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);



                string rootPatch = PatchUtil.ResRootPatch + AppConfig.Instance.excelResPath;
                string[] filesPath = Directory.GetFiles(rootPatch, "*.xlsx", SearchOption.AllDirectories);
                Console.WriteLine("在路径：" + AppConfig.Instance.excelResPath + "下面搜索所有excel文件进行数据转换");
                ExportClass.Start();
                for (int i = 0; i < filesPath.Length; i++)
                {
                    string targetFile = filesPath[i];
                    targetFile = targetFile.Replace("//", "/");
                    if (targetFile.StartsWith("~$"))
                        continue;
                    Console.WriteLine(targetFile);
                    ExcelData.ReadExcelHead(targetFile, "");
                }
                ExportClass.End();
                Console.WriteLine("导出文件结束");
            //}
        }




        public static void ExcenToData(string patch, string target)
        {
            ReadExcelHead(patch, target);
        }

        public static void ExcenToData(string patch)
        {
            ReadExcelHead(patch);
        }

        public static DataRowCollection feedExceltoORM(string path, string target = "")
        {
            Console.WriteLine("开始读取的表格是:" + path);
            //用来存储表头的配置信息
            Dictionary<string, string> excelInfo = new Dictionary<string, string>();
            //用来存储字段的存储的数据类型
            Dictionary<string, string> excelType = new Dictionary<string, string>();

            ///开始读取表格
            DataRowCollection collect = ExcelAccess.ReadExcel(path, "config");
            //将excel表格中的配置信息放入到字典当中
            for (int i = 0; i < collect.Count; i++)
            {
                Console.WriteLine("配置：" + collect[i][0] + "为：" + collect[i][1]);
                excelInfo[collect[i][0].ToString()] = collect[i][1].ToString();
            }

            string ormTableName = excelInfo["fileName"];
            //var table = dbORM[ormTableName];



            ///开始读取表格
            DataRowCollection collect1 = ExcelAccess.ReadExcel(path, excelInfo["dataTable"]);
            return collect1;
            /*
            if ( AppConfig.Instance.exportCshartClass)
                ExportClass.WriteClass(excelType, excelInfo["fileName"]);
            if ( AppConfig.Instance.exportTypescriptClass)
                ExportClass.WriteTypescriptClass(excelType, excelInfo["fileName"], target);
            ExportClass.WriteORM(excelType, excelInfo["fileName"]);
            WriteByteData(collect1, excelType, excelInfo, target);
            */
        }

        /// <summary>
        /// 指定路径excel生成数据
        /// </summary>
        /// <param name="patch"></param>
        public static void ReadExcelHead(string patch, string target = "")
        {
            Console.WriteLine("开始读取的表格是:" + patch);
            //用来存储表头的配置信息
            Dictionary<string, string> excelInfo = new Dictionary<string, string>();
            //用来存储字段的存储的数据类型
            Dictionary<string, string> excelType = new Dictionary<string, string>();

            try {
                ///开始读取表格
                DataRowCollection collect = ExcelAccess.ReadExcel(patch, "config");
                //将excel表格中的配置信息放入到字典当中
                for (int i = 0; i < collect.Count; i++)
                {
                    Console.WriteLine("配置：" + collect[i][0] + "为：" + collect[i][1]);
                    excelInfo[collect[i][0].ToString()] = collect[i][1].ToString();
                }

                ///开始读取表格
                DataRowCollection collect1 = ExcelAccess.ReadExcel(patch, excelInfo["dataTable"]);
                for (int t = 0; t < collect1[0].ItemArray.Length; t++)
                {
                    excelType[collect1[0][t].ToString()] = collect1[1][t].ToString() + "," + collect1[2][t].ToString();
                    // Console.WriteLine("数据类型：" + collect1[0][t] + "为：" + excelType[collect1[0][t].ToString()]);
                }

                var filep = patch.Split('/');
                ExportClass.WriteORM(excelType, excelInfo["fileName"], filep[filep.Length - 1]);
                WriteByteData(collect1, excelType, excelInfo, target);
            }
            catch (Exception ex) {
                Console.WriteLine(ex.Message);
            }
        }

        public static FileStream sfs = null;
        public static BinaryWriter sfile = null;
        /// <summary>
        /// 将excel的数据写入成为二进制数据
        /// </summary>
        /// <param name="collect1">Collect1.</param>
        /// <param name="excelType">Excel type.</param>
        /// <param name="excelInfo">Excel info.</param>
        /// <param name="split">If set to write singe file <c>true</c> split.</param>
        public static void WriteByteData(DataRowCollection collect1, Dictionary<string, string> excelType, Dictionary<string, string> excelInfo, string CurDir = "")
        {

            //设置生成的二进制数据目录，我这里是直接放到了项目下 
            if (CurDir == "")
            {
                CurDir = AppConfig.Instance.excelbinDataPatch + "/";
            }
            //CurDir +=  excelInfo["fileName"]+"/";

            //判断文件夹是否存在,不存在就创建这个文件夹
            if (!Directory.Exists(CurDir))
            {
                Directory.CreateDirectory(CurDir);
            }
            Console.WriteLine(CurDir);
            string FilePath = CurDir + excelInfo["fileName"] + ".jason";

            if (File.Exists(FilePath))
            {
                try
                {
                    File.Delete(FilePath);
                }
                catch (Exception e)
                {
                    Console.WriteLine("删除文件出错 路径为：" + FilePath + "    错误为：" + e.ToString());
                    return;
                }
            }

            FileStream fs = new FileStream(FilePath, FileMode.CreateNew);
            BinaryWriter file = new BinaryWriter(fs);

            ///写入头文件信息的数量
            file.Write(excelInfo.Count);
            ///写入版本的信息
            foreach (var item in excelInfo)

            {
                byte[] bytes = ExcelAccess.GetUTFBytes(item.Key);
                if (bytes.Length > 65535)
                    throw new Exception("字符串长度超出限制 65535");
                file.Write((ushort)bytes.Length);
                file.Write(bytes);


                byte[] bytes2 = ExcelAccess.GetUTFBytes(item.Value);
                if (bytes2.Length > 65535)
                    throw new Exception("字符串长度超出限制 65535");
                file.Write((ushort)bytes2.Length);
                file.Write(bytes2);
            }

            ////写入数据的行和列数总量
            file.Write(collect1.Count - 3);
            file.Write(collect1[0].ItemArray.Length);

            ///开始写入表格的所有数据
            ///
            for (int t = 3; t < collect1.Count; t++)
            {
                for (int i = 0; i < collect1[t].ItemArray.Length; i++)
                {
                    if (t > 160)
                    {
                        //Console.WriteLine("t:" + t + " i:" + i);
                        //Console.WriteLine("数据类型：" + collect1[0][i] + "为：" + excelType[collect1[0][i].ToString()].Split(',')[0] + "数值为" + collect1[t][i] + "===========看到没有二货策划你写了一个空格进去");
                    }


                    if (collect1[t][i].ToString().IndexOf(' ') != -1 && collect1[t][i].ToString().Length < 2)
                    {
                        Console.WriteLine("t:" + t + " i:" + i);

                        Console.WriteLine("数据类型：" + collect1[0][i] + "为：" + excelType[collect1[0][i].ToString()].Split(',')[0] + "数值为" + collect1[t][i] + "===========看到没有二货策划你写了一个空格进去");
                    }

                    //根据需求生成单个文件和全集文件
                    if (excelInfo["split"] == "yes")
                    {
                        if (i == 0)
                        {
                            string sFilePath = CurDir + excelInfo["fileName"] + collect1[t][i].ToString() + ".json";
                            //Console.WriteLine("配置需要生成单个文件"+ sFilePath);
                            if (File.Exists(sFilePath))
                            {
                                try
                                {

                                    File.Delete(sFilePath);
                                }
                                catch (Exception e)
                                {

                                    Console.WriteLine("删除文件出错 路径为：" + sFilePath + "    错误为：" + e.ToString());
                                    Console.WriteLine("这个问题一定是第一列的id为空了导致一直产生0的id，第一列是id必须有值不允许为0" + excelInfo["fileName"]);
                                    return;
                                }
                            }
                            sfs = new FileStream(sFilePath, FileMode.CreateNew);
                            sfile = new BinaryWriter(sfs);
                            ///写入头文件信息的数量
                            sfile.Write(excelInfo.Count);
                            ///写入版本的信息
                            foreach (var item in excelInfo)
                            {
                                sfile.Write(item.Key);
                                sfile.Write(item.Value);
                            }

                            sfile.Write(1);
                            sfile.Write(collect1[0].ItemArray.Length);
                        }
                        ExcelAccess.TypeToValue(excelType[collect1[0][i].ToString()].Split(',')[0], collect1[t][i].ToString(), sfile);
                    }

                    string dataString = collect1[t][i].ToString();
                    string dataType = excelType[collect1[0][i].ToString()].Split(',')[0];
                    bool isArray = ExportClass.isArray(dataType);
                    if (isArray)
                    {
                        string[] chunks = dataString == "" ? new string[0] : dataString.Split(',');
                        string chunkType = dataType[dataType.Length - 1] == 's' ? dataType.Remove(dataType.Length - 1) : dataType.Replace("[]", "");
                        // 数组长度 - 4个字节
                        ExcelAccess.TypeToValue("uint", chunks.Length.ToString(), file);
                        for (int cur = 0; cur < chunks.Length; cur++)
                        {
                            ExcelAccess.TypeToValue(chunkType, chunks[cur], file);
                        }
                    }
                    else
                    {
                        ExcelAccess.TypeToValue(excelType[collect1[0][i].ToString()].Split(',')[0], collect1[t][i].ToString(), file);
                    }


                }
                if (sfile != null)
                {
                    sfile.Close();
                    sfile = null;
                    sfs.Close();
                    sfs.Dispose();
                }
            }
            //关闭文件
            file.Close();
            file = null;
            fs.Close();
            fs.Dispose();

            Console.WriteLine("导出文件结束（结束不代表一定成功）");
        }


        public static bool ReadExcelType(string dataFilePath, out Dictionary<string, string>? inExcelType)
        {
            string baseFileName = Path.GetFileNameWithoutExtension(dataFilePath);

            string excelFolderPatch = PatchUtil.ResRootPatch + AppConfig.Instance.excelResPath;
            string[] excelPaths = Directory.GetFiles(excelFolderPatch, "*.xlsx", SearchOption.AllDirectories);

            inExcelType = null;
            //用来存储表头的配置信息
            Dictionary<string, string> excelInfo = new Dictionary<string, string>();
            //用来存储字段的存储的数据类型
            Dictionary<string, string> excelType = new Dictionary<string, string>();

            foreach (string tmpPath in excelPaths)
            {

                if (Path.GetFileNameWithoutExtension(tmpPath.Replace("\\", "/")) == baseFileName)
                {
                    //Console.WriteLine(baseFileName);
                    ///开始读取表格
                    DataRowCollection collect = ExcelAccess.ReadExcel(tmpPath.Replace("\\", "/"), "config");
                    //将excel表格中的配置信息放入到字典当中
                    for (int i = 0; i < collect.Count; i++)
                    {
                        excelInfo[collect[i][0].ToString()] = collect[i][1].ToString();
                    }

                    DataRowCollection collectData = ExcelAccess.ReadExcel(tmpPath.Replace("\\", "/"), excelInfo["dataTable"]);
                    for (int t = 0; t < collectData[0].ItemArray.Length; t++)
                    {

                        excelType[collectData[0][t].ToString()] = collectData[1][t].ToString();

                    }
                    inExcelType = excelType;

                    return true;

                }
            }

            Console.WriteLine("没找到同名的excel表格");
            return false;

        }


    }

}