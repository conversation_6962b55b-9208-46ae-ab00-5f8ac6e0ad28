﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SaveDataService;
using SaveDataService.Manage;

namespace ComfyuiGate
{
    /// <summary>
    /// CafelabsTest - ComfyUI工作流调用类
    /// 基于文件: cafelabs-test.json
    /// 自动生成时间: 2025-06-05 16:48:27
    /// </summary>
    public class CafelabsTest
    {
        /// <summary>
        /// 工作流JSON定义
        /// </summary>
        private const string WORKFLOW_JSON = @"{
  ""3"": {
    ""inputs"": {
      ""seed"": 556330059542208,
      ""steps"": 20,
      ""cfg"": 8,
      ""sampler_name"": ""euler"",
      ""scheduler"": ""normal"",
      ""denoise"": 1,
      ""model"": [
        ""4"",
        0
      ],
      ""positive"": [
        ""6"",
        0
      ],
      ""negative"": [
        ""7"",
        0
      ],
      ""latent_image"": [
        ""5"",
        0
      ]
    },
    ""class_type"": ""KSampler"",
    ""_meta"": {
      ""title"": ""K采样器""
    }
  },
  ""4"": {
    ""inputs"": {
      ""ckpt_name"": ""v1-5-pruned-emaonly-fp16.safetensors""
    },
    ""class_type"": ""CheckpointLoaderSimple"",
    ""_meta"": {
      ""title"": ""Checkpoint加载器（简易）""
    }
  },
  ""5"": {
    ""inputs"": {
      ""width"": 512,
      ""height"": 512,
      ""batch_size"": 1
    },
    ""class_type"": ""EmptyLatentImage"",
    ""_meta"": {
      ""title"": ""空Latent图像""
    }
  },
  ""6"": {
    ""inputs"": {
      ""text"": ""beautiful scenery nature glass bottle landscape, , purple galaxy bottle,"",
      ""clip"": [
        ""4"",
        1
      ]
    },
    ""class_type"": ""CLIPTextEncode"",
    ""_meta"": {
      ""title"": ""input-promt-提示词""
    }
  },
  ""7"": {
    ""inputs"": {
      ""text"": ""text, watermark"",
      ""clip"": [
        ""4"",
        1
      ]
    },
    ""class_type"": ""CLIPTextEncode"",
    ""_meta"": {
      ""title"": ""input-fumianPromt-负面提示词""
    }
  },
  ""8"": {
    ""inputs"": {
      ""samples"": [
        ""3"",
        0
      ],
      ""vae"": [
        ""4"",
        2
      ]
    },
    ""class_type"": ""VAEDecode"",
    ""_meta"": {
      ""title"": ""VAE解码""
    }
  },
  ""10"": {
    ""inputs"": {
      ""images"": [
        ""8"",
        0
      ]
    },
    ""class_type"": ""PreviewImage"",
    ""_meta"": {
      ""title"": ""预览图像""
    }
  }
}";

        /// <summary>
        /// 运行工作流
        /// </summary>
        /// <param name="promt_text">input-promt-提示词 - text</param>
        /// <param name="fumianPromt_text">input-fumianPromt-负面提示词 - text</param>
        /// <returns>任务ID</returns>
        public static string runWorkflow(string promt_text = "beautiful scenery nature glass bottle landscape, , purple galaxy bottle,", string fumianPromt_text = "text, watermark")
        {
            try
            {
                // 解析工作流JSON
                var workflow = JsonConvert.DeserializeObject<JObject>(WORKFLOW_JSON);
                if (workflow == null)
                {
                    throw new Exception("无法解析工作流JSON");
                }

                // 更新输入参数
                // 检查JSON格式并相应更新参数
                if (workflow["nodes"] != null)
                {
                    // 新格式：包含nodes数组
                    var nodesArray = workflow["nodes"] as JArray;
                    if (nodesArray != null)
                    {
                        // 更新节点 6 的 text 参数
                        var node6 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "6") as JObject;
                        if (node6 != null)
                        {
                            var inputs = node6["inputs"] as JArray;
                            if (inputs != null)
                            {
                                var inputIndex = inputs.ToList().FindIndex(i => i["name"]?.ToString() == "text");
                                if (inputIndex >= 0)
                                {
                                    var widgetValues = node6["widgets_values"] as JArray;
                                    if (widgetValues != null && inputIndex < widgetValues.Count)
                                    {
                                        widgetValues[inputIndex] = JToken.FromObject(promt_text);
                                    }
                                }
                            }
                        }

                        // 更新节点 7 的 text 参数
                        var node7 = nodesArray.FirstOrDefault(n => n["id"]?.ToString() == "7") as JObject;
                        if (node7 != null)
                        {
                            var inputs = node7["inputs"] as JArray;
                            if (inputs != null)
                            {
                                var inputIndex = inputs.ToList().FindIndex(i => i["name"]?.ToString() == "text");
                                if (inputIndex >= 0)
                                {
                                    var widgetValues = node7["widgets_values"] as JArray;
                                    if (widgetValues != null && inputIndex < widgetValues.Count)
                                    {
                                        widgetValues[inputIndex] = JToken.FromObject(fumianPromt_text);
                                    }
                                }
                            }
                        }

                    }
                }
                else
                {
                    // 旧格式：直接以节点ID为键
                    // 更新节点 6 的 text 参数
                    if (workflow["6"]?["inputs"]?["text"] != null)
                    {
                        workflow["6"]["inputs"]["text"] = JToken.FromObject(promt_text);
                    }

                    // 更新节点 7 的 text 参数
                    if (workflow["7"]?["inputs"]?["text"] != null)
                    {
                        workflow["7"]["inputs"]["text"] = JToken.FromObject(fumianPromt_text);
                    }

                }
                // 提交工作流到ComfyUI
                string updatedJson = JsonConvert.SerializeObject(workflow);
                var comfyUIManage = ComfyUIManage.Instance;

                // 首先将工作流保存到数据库（如果不存在）
                string workflowId = EnsureWorkflowExists(updatedJson, "CafelabsTest");
                if (string.IsNullOrEmpty(workflowId))
                {
                    Console.WriteLine("无法保存工作流到数据库");
                    return "";
                }

                // 创建任务
                string taskId = comfyUIManage.CreateTask(workflowId, "CafelabsTest_Task", "system");
                if (string.IsNullOrEmpty(taskId))
                {
                    Console.WriteLine("无法创建任务");
                    return "";
                }

                Console.WriteLine($"工作流已提交，任务ID: {taskId}");
                return taskId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"运行工作流失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务状态信息</returns>
        public static string GetTaskStatus(string taskId)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                var task = comfyUIManage.GetTaskById(taskId);
                if (task != null)
                {
                    return JsonConvert.SerializeObject(task, Formatting.Indented);
                }
                return "任务不存在";
            }
            catch (Exception ex)
            {
                return $"获取任务状态失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 确保工作流存在于数据库中
        /// </summary>
        /// <param name="workflowJson">工作流JSON</param>
        /// <param name="workflowName">工作流名称</param>
        /// <returns>工作流ID</returns>
        private static string EnsureWorkflowExists(string workflowJson, string workflowName)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;

                // 计算工作流的哈希值作为唯一标识
                string workflowHash = System.Security.Cryptography.SHA1.Create()
                    .ComputeHash(System.Text.Encoding.UTF8.GetBytes(workflowJson))
                    .Aggregate("", (s, b) => s + b.ToString("x2"));

                // 检查是否已存在
                var existingWorkflow = comfyUIManage.GetWorkflowById(workflowHash);
                if (existingWorkflow != null)
                {
                    Console.WriteLine($"工作流已存在: {workflowHash}");
                    return workflowHash;
                }

                // 创建新的工作流
                string workflowId = comfyUIManage.AddWorkflow(
                    workflowName,
                    workflowJson,
                    "generated",
                    $"自动生成的工作流: {workflowName}",
                    "system"
                );

                Console.WriteLine($"创建新工作流: {workflowId}");
                return workflowId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"确保工作流存在失败: {ex.Message}");
                return "";
            }
        }

    }
}
