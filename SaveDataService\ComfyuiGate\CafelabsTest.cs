using System;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace SaveDataService.ComfyuiGate
{
    /// <summary>
    /// CafelabsTest - ComfyUI工作流
    /// </summary>
    public class CafelabsTest
    {
        /// <summary>
        /// 原始工作流JSON
        /// </summary>
        private static readonly string OriginalWorkflowJson = @"
{
  ""3"": {
    ""inputs"": {
      ""seed"": 556330059542208,
      ""steps"": 20,
      ""cfg"": 8,
      ""sampler_name"": ""euler"",
      ""scheduler"": ""normal"",
      ""denoise"": 1,
      ""model"": [
        ""4"",
        0
      ],
      ""positive"": [
        ""6"",
        0
      ],
      ""negative"": [
        ""7"",
        0
      ],
      ""latent_image"": [
        ""5"",
        0
      ]
    },
    ""class_type"": ""KSampler"",
    ""_meta"": {
      ""title"": ""K采样器""
    }
  },
  ""4"": {
    ""inputs"": {
      ""ckpt_name"": ""v1-5-pruned-emaonly-fp16.safetensors""
    },
    ""class_type"": ""CheckpointLoaderSimple"",
    ""_meta"": {
      ""title"": ""Checkpoint加载器（简易）""
    }
  },
  ""5"": {
    ""inputs"": {
      ""width"": 512,
      ""height"": 512,
      ""batch_size"": 1
    },
    ""class_type"": ""EmptyLatentImage"",
    ""_meta"": {
      ""title"": ""空Latent图像""
    }
  },
  ""6"": {
    ""inputs"": {
      ""text"": ""beautiful scenery nature glass bottle landscape, , purple galaxy bottle,"",
      ""clip"": [
        ""4"",
        1
      ]
    },
    ""class_type"": ""CLIPTextEncode"",
    ""_meta"": {
      ""title"": ""input-promt-提示词""
    }
  },
  ""7"": {
    ""inputs"": {
      ""text"": ""text, watermark"",
      ""clip"": [
        ""4"",
        1
      ]
    },
    ""class_type"": ""CLIPTextEncode"",
    ""_meta"": {
      ""title"": ""input-fumianPromt-负面提示词""
    }
  },
  ""8"": {
    ""inputs"": {
      ""samples"": [
        ""3"",
        0
      ],
      ""vae"": [
        ""4"",
        2
      ]
    },
    ""class_type"": ""VAEDecode"",
    ""_meta"": {
      ""title"": ""VAE解码""
    }
  },
  ""10"": {
    ""inputs"": {
      ""images"": [
        ""8"",
        0
      ]
    },
    ""class_type"": ""PreviewImage"",
    ""_meta"": {
      ""title"": ""预览图像""
    }
  }
}
        ";

        /// <summary>
        /// 执行工作流
        /// </summary>
        /// <param name="steps">采样步数</param>
        /// <param name="cfg">CFG引导强度</param>
        /// <param name="width">图片宽度</param>
        /// <param name="height">图片高度</param>
        /// <param name="text">文本提示词</param>
        /// <param name="text">文本提示词</param>
        /// <param name="serverUrl">ComfyUI服务器地址（可选）</param>
        /// <returns>执行结果</returns>
        public static async Task<string> runWorkflow(int steps = 20, double cfg = 8.0d, int width = 512, int height = 512, string text = "beautiful scenery nature glass bottle landscape, , purple galaxy bottle,", string text = "text, watermark", string serverUrl = null)
        {
            try
            {
                var workflow = JObject.Parse(OriginalWorkflowJson);
                var comfyUIManage = new ComfyUIManage();

                workflow["3"]["inputs"]["steps"] = steps;
                workflow["3"]["inputs"]["cfg"] = cfg;
                workflow["5"]["inputs"]["width"] = width;
                workflow["5"]["inputs"]["height"] = height;
                workflow["6"]["inputs"]["text"] = text;
                workflow["7"]["inputs"]["text"] = text;

                var result = await comfyUIManage.ExecuteWorkflowAsync(workflow.ToString(), serverUrl);
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception($"CafelabsTest执行失败: {ex.Message}", ex);
            }
        }
    }
}
